from fpdf import FPDF

pdf = FPDF()
pdf.add_page()
pdf.set_font("Arial", size=12)

de_thi = [
    "I. Trắc nghiệm: Kho<PERSON>h tròn vào đáp án đúng nhất.",
    "",
    "Câu 1. <PERSON><PERSON><PERSON> hồ chỉ 7 giờ 15 phút. <PERSON> giờ và kim phút sẽ ở vị trí nào?",
    "<PERSON><PERSON> giờ giữa số 7 và 8, kim ph<PERSON><PERSON> chỉ số 3",
    "<PERSON><PERSON> <PERSON> giờ chỉ số 7, kim ph<PERSON><PERSON> chỉ số 12",
    "<PERSON><PERSON> giờ chỉ số 8, kim ph<PERSON><PERSON> chỉ số 9",
    "<PERSON><PERSON> giờ chỉ số 6, kim ph<PERSON><PERSON> chỉ số 3",
    "",
    "Câu 2. Số nào nhỏ hơn 105 nhưng lớn hơn 100?",
    "A. 110  B. 100  C. 104  D. 99",
    "",
    "Câu 3. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hàng c<PERSON> 120 quyển sách. <PERSON><PERSON><PERSON> thứ nh<PERSON>t b<PERSON> đ<PERSON>ợc 35 quyển, ng<PERSON><PERSON> thứ hai bán đượ<PERSON> 48 quyển. Hỏi còn lại bao nhiêu quyển sách?",
    "A<PERSON> 38  <PERSON>. 42  <PERSON>. 37  <PERSON>. 43",
    "",
    "<PERSON>âu 4. <PERSON>ôm nay là th<PERSON> Sáu ngày 13 tháng 12. Ngày mai là:",
    "A. Thứ <PERSON><PERSON>y ngày 14 tháng 12",
    "<PERSON>. Chủ Nhật ngày 15 tháng 12",
    "<PERSON>. Thứ N<PERSON>m ngày 12 tháng 12",
    "D. Thứ Sáu ngày 20 tháng 12",
    "",
    "Câu 5. Một năm có bao nhiêu tháng có 31 ngày?",
    "A. 6  B. 7  C. 12  D. 5",
    "",
    "Câu 6. Cái thước dài 1m bằng bao nhiêu xăng-ti-mét?",
    "A. 100cm  B. 10cm  C. 1000cm  D. 1cm",
    "",
    "Câu 7. Số nào khi cộng với 25 thì được 80?",
    "A. 55  B. 60  C. 45  D. 65",
    "",
    "Câu 8. Một bình nước có dung tích 2 lít. Nếu đổ ra 750ml thì còn lại:",
    "A. 1 lít 25ml  B. 1 lít 50ml  C. 1 lít 75ml  D. 1 lít 250ml",
    "",
    "II. Tự luận",
    "",
    "Bài 1. Đặt tính rồi tính:",
    "75 – 29",
    "68 + 14",
    "96 – 47",
    "59 + 38",
    "",
    "Bài 2. Tính:",
    "1m = … cm",
    "48cm + 35cm = … cm",
    "100 – 47 = …",
    "… + 28 = 91",
    "",
    "Bài 3. Điền dấu +, – thích hợp vào chỗ chấm:",
    "72 … 36 = 108",
    "94 … 18 = 76",
    "45 … 19 = 26",
    "67 … 42 = 25",
    "",
    "Bài 4. Một xe tải chở được 124 bao gạo. Buổi sáng xe chở được 56 bao, buổi chiều chở được thêm 48 bao. Hỏi xe còn chở được bao nhiêu bao nữa?",
    "Bài giải: …",
    "Đáp số: … bao gạo",
    "",
    "Bài 5. Một cuộn dây dài 2 mét. Cắt đi 85 cm. Hỏi còn lại bao nhiêu xăng-ti-mét dây?",
    "Bài giải: …",
    "Đáp số: … cm"
]

for line in de_thi:
    pdf.cell(200, 10, txt=line, ln=True)

pdf.output("De_thi_Toan_lop_2.pdf")
