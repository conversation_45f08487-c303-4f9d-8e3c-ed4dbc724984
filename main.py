import requests
from bs4 import BeautifulSoup
import re
import os
import time

class NCTDownloader:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.nhaccuatui.com/'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def get_playlist_info(self, playlist_url):
        """Extract playlist information including songs directly from HTML"""
        try:
            response = self.session.get(playlist_url)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # Create a structure similar to the old JSON format
            playlist_data = {'data': {}}

            # Extract playlist title
            title_tag = soup.find('title')
            if title_tag:
                title_text = title_tag.text.split(' - ')[0].strip()
                playlist_data['data']['title'] = title_text
            else:
                playlist_data['data']['title'] = 'NCT_Playlist'

            print(f"Playlist title: {playlist_data['data']['title']}")

            # Find all song items in the playlist
            song_items = soup.find_all('div', class_='item_content')
            songs = []

            print(f"Found {len(song_items)} songs in the playlist")

            for item in song_items:
                song = {}

                # Extract song ID from the href attribute
                song_link = item.find('a', class_='name_song')
                if song_link and 'href' in song_link.attrs:
                    href = song_link['href']
                    song_id = href.split('.')[-2]
                    song['key'] = song_id

                    # Extract song title
                    song['title'] = song_link.text.strip()

                # Extract artists
                artist_links = item.find_all('a', class_='name_singer')
                if artist_links:
                    artists = [{'name': artist.text.strip()} for artist in artist_links]
                    song['artists'] = artists
                else:
                    song['artists'] = [{'name': 'Unknown Artist'}]

                songs.append(song)

            playlist_data['data']['listSong'] = songs
            return playlist_data

        except Exception as e:
            print(f"Error fetching playlist info: {e}")
            return None

    def get_song_stream_url(self, song_id):
        """Get the streaming URL for a song by its ID using vuiz.net service"""
        try:
            if not song_id:
                print("No song ID provided")
                return None

            # Construct the full song URL
            song_url = f"https://www.nhaccuatui.com/bai-hat/{song_id}.html"
            print(f"Song URL: {song_url}")

            # Use vuiz.net service to get the download link
            vuiz_url = "https://m.vuiz.net/getlink/nhaccuatui/api.php"
            print(f"Using vuiz.net service to get download link...")

            # Use full headers and cookies to mimic a real browser
            headers = {
                'accept': '*/*',
                'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
                'content-type': 'multipart/form-data; boundary=----WebKitFormBoundaryJcZ39E86fc5jQ1kg',
                'origin': 'https://m.vuiz.net',
                'priority': 'u=1, i',
                'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'x-requested-with': 'XMLHttpRequest'
            }

            # Add cookies
            cookies = {
                '_ga': 'GA1.1.1713903562.1745372313',
                'SL_G_WPT_TO': 'vi',
                'SL_GWPT_Show_Hide_tmp': '1',
                'SL_wptGlobTipTmp': '1',
                '_ga_3PTXHZCJ6E': 'GS1.1.1745372312.1.1.1745372349.0.0.0'
            }

            # Create multipart/form-data payload
            data = f'------WebKitFormBoundaryJcZ39E86fc5jQ1kg\r\nContent-Disposition: form-data; name="link"\r\n\r\n{song_url}\r\n------WebKitFormBoundaryJcZ39E86fc5jQ1kg--\r\n'

            # Send the request with all the headers, cookies and data
            response = self.session.post(vuiz_url, data=data, headers=headers, cookies=cookies)
            response.raise_for_status()

            # The API returns JSON data
            try:
                data = response.json()

                # Check if the response contains success key with HTML content
                if data and isinstance(data, dict) and 'success' in data:
                    html_content = data['success']

                    # Use regex to find all download links in the HTML content
                    import re

                    # Look for download links with class 128 (128kbps)
                    download_links_128 = re.findall(r'class=\\?["\\]128["\\].*?href=\\?"(https?:[^"]+\.mp3[^"]*)', html_content)
                    if download_links_128:
                        # Unescape the URL
                        stream_url = download_links_128[0].replace('\\/', '/')
                        print(f"Found download link (128kbps): {stream_url[:50]}...")
                        return stream_url

                    # Look for data-src attribute with mp3 URL in amazingaudioplayer-source
                    data_src_links = re.findall(r'data-src=\\?"(https?:[^"]+\.mp3[^"]*)', html_content)
                    if data_src_links:
                        # Unescape the URL
                        stream_url = data_src_links[0].replace('\\/', '/')
                        print(f"Found stream URL in data-src: {stream_url[:50]}...")
                        return stream_url

                    # Try to find any mp3 URL in the HTML content
                    all_mp3_links = re.findall(r'(https?:[^"]+\.mp3[^"]*)', html_content)
                    if all_mp3_links:
                        # Unescape the URL
                        stream_url = all_mp3_links[0].replace('\\/', '/')
                        print(f"Found mp3 URL in HTML: {stream_url[:50]}...")
                        return stream_url

                    # Parse the HTML content as a fallback
                    inner_soup = BeautifulSoup(html_content.replace('\\"', '"').replace('\\/', '/'), 'html.parser')

                    # Look for download links in the table
                    download_links = inner_soup.find_all('a', class_='128')
                    for link in download_links:
                        href = link.get('href')
                        if href and href != 'D' and ('.mp3' in href or '.m4a' in href):
                            print(f"Found download link (128kbps) with BeautifulSoup: {href[:50]}...")
                            return href

                    # Look for the amazingaudioplayer-source div with data-src attribute
                    source_divs = inner_soup.find_all('div', class_='amazingaudioplayer-source')
                    for source_div in source_divs:
                        if source_div and source_div.get('data-src') and source_div.get('data-src') != 'D':
                            stream_url = source_div.get('data-src')
                            print(f"Found stream URL in data-src with BeautifulSoup: {stream_url[:50]}...")
                            return stream_url

                    print("Could not find stream URL in API response HTML")
                    return None
                else:
                    print("Could not find success key in API response")
                    return None
            except Exception as e:
                print(f"Error parsing API response: {e}")

                # Try to parse as HTML in case the response is not JSON
                try:
                    # Try to find any mp3 URL in the response text
                    import re
                    all_mp3_links = re.findall(r'(https?://stream\.nixcdn\.com/[^"]+\.mp3[^"]*)', response.text)
                    if all_mp3_links:
                        stream_url = all_mp3_links[0]
                        print(f"Found mp3 URL in response text: {stream_url[:50]}...")
                        return stream_url

                    soup = BeautifulSoup(response.text, 'html.parser')

                    # Look for download links in the response
                    download_links = soup.find_all('a', class_='128')
                    for link in download_links:
                        href = link.get('href')
                        if href and href != 'D' and ('.mp3' in href or '.m4a' in href):
                            print(f"Found download link (128kbps): {href[:50]}...")
                            return href

                    # Look for the amazingaudioplayer-source div with data-src attribute
                    source_divs = soup.find_all('div', class_='amazingaudioplayer-source')
                    for source_div in source_divs:
                        if source_div and source_div.get('data-src') and source_div.get('data-src') != 'D':
                            stream_url = source_div.get('data-src')
                            print(f"Found stream URL in data-src: {stream_url[:50]}...")
                            return stream_url

                    # Try to find any link that might be a download link
                    all_links = soup.find_all('a')
                    for link in all_links:
                        href = link.get('href')
                        if href and href != 'D' and ('.mp3' in href or '.m4a' in href):
                            print(f"Found alternative stream URL: {href[:50]}...")
                            return href
                except Exception as e:
                    print(f"Error parsing response as HTML: {e}")

                print("Could not find stream URL from vuiz.net service")
                return None

        except Exception as e:
            print(f"Error getting stream URL for song {song_id}: {e}")
            return None

    def download_song(self, stream_url, save_path):
        """Download a song from its streaming URL"""
        try:
            # Print the URL for debugging
            print(f"  Stream URL: {stream_url}")

            # Download the song
            response = self.session.get(stream_url, stream=True)
            response.raise_for_status()

            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            return True

        except Exception as e:
            print(f"Error downloading song: {e}")
            return False

    def download_playlist(self, playlist_url, output_dir="downloads"):
        """Download all songs from a playlist"""
        playlist_data = self.get_playlist_info(playlist_url)

        if not playlist_data or 'data' not in playlist_data:
            print("Failed to get playlist data")
            return

        playlist_title = playlist_data.get('data', {}).get('title', 'NCT_Playlist')
        playlist_dir = os.path.join(output_dir, re.sub(r'[\\/*?:"<>|]', "", playlist_title))

        if not os.path.exists(playlist_dir):
            os.makedirs(playlist_dir)

        songs = playlist_data.get('data', {}).get('listSong', [])
        total_songs = len(songs)

        print(f"Found {total_songs} songs in playlist: {playlist_title}")

        for index, song in enumerate(songs):
            song_id = song.get('key')
            song_title = song.get('title', 'Unknown')
            artists = song.get('artists', [])
            artist_names = ", ".join([artist.get('name', '') for artist in artists])

            filename = f"{song_title} - {artist_names}.mp3"
            # Clean filename of invalid characters
            filename = re.sub(r'[\\/*?:"<>|]', "", filename)

            song_path = os.path.join(playlist_dir, filename)

            print(f"[{index+1}/{total_songs}] Downloading: {song_title} - {artist_names}")

            stream_url = self.get_song_stream_url(song_id)
            if not stream_url:
                print(f"  Cannot find stream URL for {song_title}. Skipping.")
                continue

            if self.download_song(stream_url, song_path):
                print(f"  Downloaded successfully to {song_path}")
            else:
                print(f"  Failed to download {song_title}")

            # Add a small delay to avoid rate limiting
            time.sleep(1)

        print(f"Download completed! All songs saved to {playlist_dir}")

# Example usage
if __name__ == "__main__":
    print("""\n=== THÔNG BÁO QUAN TRỌNG ===\n
    Công cụ này hiện không thể tải nhạc từ nhaccuatui.com.

    Nguyên nhân:
    1. Trang web nhaccuatui.com đã thay đổi cách họ cung cấp URL stream.
    2. Dịch vụ vuiz.net đã thay đổi cách hoạt động và đã mã hóa URL tải xuống (thay thế bằng ký tự "D").
    3. Phản hồi API hiện tại không chứa URL tải xuống thực tế nữa, khác với phản hồi trước đây.

    Giải pháp:
    1. Sử dụng trang web chính thức nhaccuatui.com để nghe nhạc.
    2. Truy cập trực tiếp vào trang vuiz.net và dán URL bài hát vào đó để tải xuống thủ công.
    3. Sử dụng script download_from_example.py để tải nhạc từ file example.json.

    Công cụ vẫn có thể hiển thị danh sách bài hát trong playlist.
    \n==============================\n""")

    # Vẫn hiển thị danh sách bài hát trong playlist
    downloader = NCTDownloader()
    playlist_url = "https://www.nhaccuatui.com/playlist/tiem-nang-v-pop-various-artists.8b8lO7A3blOc.html"
    downloader.download_playlist(playlist_url)