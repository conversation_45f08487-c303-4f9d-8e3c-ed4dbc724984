import json
import os
import re
import requests
from bs4 import BeautifulSoup

def download_from_example_json():
    try:
        # Đọc file example.json
        with open("example.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        
        print("Successfully loaded example.json")
        
        # Kiểm tra xem file có chứa key 'success' với nội dung HTML không
        if data and isinstance(data, dict) and 'success' in data:
            html_content = data['success']
            
            # Parse HTML content
            soup = BeautifulSoup(html_content.replace('\\"', '"').replace('\\/', '/'), 'html.parser')
            
            # Tìm tất cả các thẻ li trong amazingaudioplayer-audios
            song_items = soup.find_all('li', attrs={'data-artist': True, 'data-title': True})
            
            print(f"Found {len(song_items)} songs in example.json")
            
            # Tạo thư mục để lưu các bài hát
            output_dir = "downloads/From_Example_JSON"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # Tìm tất cả các thẻ div với class amazingaudioplayer-source
            source_divs = soup.find_all('div', class_='amazingaudioplayer-source')
            
            # Tìm tất cả các thẻ a với class 128
            download_links = soup.find_all('a', class_='128')
            
            # Tạo danh sách các bài hát và URL tải xuống
            songs = []
            for i, item in enumerate(song_items):
                artist = item.get('data-artist')
                title = item.get('data-title')
                
                # Tìm URL tải xuống từ source_divs
                stream_url = None
                if i < len(source_divs):
                    stream_url = source_divs[i].get('data-src')
                
                # Nếu không tìm thấy URL từ source_divs, thử tìm từ download_links
                if not stream_url and i < len(download_links):
                    stream_url = download_links[i].get('href')
                
                if stream_url and stream_url != 'D':
                    songs.append({
                        'title': title,
                        'artist': artist,
                        'stream_url': stream_url
                    })
            
            print(f"Found {len(songs)} songs with valid download URLs")
            
            # Tải xuống các bài hát
            session = requests.Session()
            for i, song in enumerate(songs):
                title = song['title']
                artist = song['artist']
                stream_url = song['stream_url']
                
                # Tạo tên file hợp lệ
                filename = f"{title} - {artist}.mp3"
                filename = re.sub(r'[\\/*?:"<>|]', "-", filename)
                
                # Tạo đường dẫn đầy đủ để lưu file
                filepath = os.path.join(output_dir, filename)
                
                print(f"[{i+1}/{len(songs)}] Downloading: {title} - {artist}")
                print(f"  Stream URL: {stream_url}")
                
                try:
                    # Tải xuống bài hát
                    response = session.get(stream_url, stream=True)
                    response.raise_for_status()
                    
                    with open(filepath, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    
                    print(f"  Downloaded successfully to {filepath}")
                except Exception as e:
                    print(f"  Error downloading song: {e}")
            
            print(f"Download completed! All songs saved to {output_dir}")
        else:
            print("Could not find 'success' key in example.json")
    except FileNotFoundError:
        print("File example.json not found")
    except json.JSONDecodeError:
        print("Error decoding JSON from example.json")
    except Exception as e:
        print(f"Error parsing example.json: {e}")

if __name__ == "__main__":
    download_from_example_json()
