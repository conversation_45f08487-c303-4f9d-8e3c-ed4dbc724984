import json
import re
from bs4 import BeautifulSoup

def parse_example_json():
    try:
        # Đ<PERSON><PERSON> file example.json
        with open("example.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        
        print("Successfully loaded example.json")
        
        # Kiểm tra xem file có chứa key 'success' với nội dung HTML không
        if data and isinstance(data, dict) and 'success' in data:
            html_content = data['success']
            
            # Lưu nội dung HTML vào file để kiểm tra
            with open("example_html.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            
            print(f"HTML content saved to example_html.html")
            
            # Sử dụng regex để tìm tất cả các link tải xuống trong nội dung HTML
            print("\nSearching for download links in HTML content...")
            
            # Tìm link trong thuộc tính href
            href_links = re.findall(r'href=\\?"(https?:[^"]+\.mp3[^"]*)', html_content)
            print(f"Found {len(href_links)} links in href attributes:")
            for i, link in enumerate(href_links):
                print(f"{i+1}. {link.replace('\\/', '/')}")
            
            # Tìm link trong thuộc tính data-src
            data_src_links = re.findall(r'data-src=\\?"(https?:[^"]+\.mp3[^"]*)', html_content)
            print(f"\nFound {len(data_src_links)} links in data-src attributes:")
            for i, link in enumerate(data_src_links):
                print(f"{i+1}. {link.replace('\\/', '/')}")
            
            # Tìm bất kỳ URL mp3 nào trong nội dung HTML
            all_mp3_links = re.findall(r'(https?:[^"]+\.mp3[^"]*)', html_content)
            print(f"\nFound {len(all_mp3_links)} mp3 URLs in HTML content:")
            for i, link in enumerate(all_mp3_links):
                print(f"{i+1}. {link.replace('\\/', '/')}")
            
            # Parse HTML content
            print("\nParsing HTML content with BeautifulSoup...")
            soup = BeautifulSoup(html_content.replace('\\"', '"').replace('\\/', '/'), 'html.parser')
            
            # Tìm thẻ div với class amazingaudioplayer-source
            source_divs = soup.find_all('div', class_='amazingaudioplayer-source')
            print(f"Found {len(source_divs)} amazingaudioplayer-source divs:")
            for i, div in enumerate(source_divs):
                print(f"{i+1}. data-src: {div.get('data-src')}")
            
            # Tìm thẻ a với class btn-success
            download_links = soup.find_all('a', class_='btn-success')
            print(f"\nFound {len(download_links)} download links:")
            for i, link in enumerate(download_links):
                print(f"{i+1}. href: {link.get('href')}")
            
            # Tìm thẻ a với class 128 hoặc 320
            quality_links = soup.find_all('a', class_=['128', '320'])
            print(f"\nFound {len(quality_links)} quality links:")
            for i, link in enumerate(quality_links):
                print(f"{i+1}. class: {link.get('class')}, href: {link.get('href')}")
        else:
            print("Could not find 'success' key in example.json")
    except FileNotFoundError:
        print("File example.json not found")
    except json.JSONDecodeError:
        print("Error decoding JSON from example.json")
    except Exception as e:
        print(f"Error parsing example.json: {e}")

if __name__ == "__main__":
    parse_example_json()
